@extends('layouts.shop')

@section('title', $product->name . ' - PolyPay Store')

@php
    use App\Helpers\CurrencyHelper;
@endphp

@section('content')
<div class="min-h-screen bg-secondary-50 dark:bg-secondary-900 py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Breadcrumb -->
        <nav class="flex mb-8 animate-fade-in-up" aria-label="Breadcrumb">
            <ol class="inline-flex items-center space-x-1 md:space-x-3">
                <li class="inline-flex items-center">
                    <a href="{{ route('shop.index') }}" class="inline-flex items-center text-sm font-medium text-secondary-700 hover:text-primary-600 dark:text-secondary-400 dark:hover:text-primary-400">
                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L9 5.414V17a1 1 0 102 0V5.414l5.293 5.293a1 1 0 001.414-1.414l-7-7z"/>
                        </svg>
                        Shop
                    </a>
                </li>
                @if($product->category)
                <li>
                    <div class="flex items-center">
                        <svg class="w-6 h-6 text-secondary-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
                        </svg>
                        <a href="{{ route('shop.category', $product->category->slug) }}" class="ml-1 text-sm font-medium text-secondary-700 hover:text-primary-600 dark:text-secondary-400 dark:hover:text-primary-400 md:ml-2">
                            {{ $product->category->name }}
                        </a>
                    </div>
                </li>
                @endif
                <li aria-current="page">
                    <div class="flex items-center">
                        <svg class="w-6 h-6 text-secondary-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
                        </svg>
                        <span class="ml-1 text-sm font-medium text-secondary-500 dark:text-secondary-400 md:ml-2">{{ $product->name }}</span>
                    </div>
                </li>
            </ol>
        </nav>

        <div class="bg-white dark:bg-secondary-800 shadow-large rounded-2xl overflow-hidden animate-fade-in-up" style="animation-delay: 0.1s;">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 p-8 lg:p-12">
                <!-- Product Image Gallery -->
                <div class="space-y-6" x-data="{ activeImage: 0 }">
                    <div class="relative aspect-square bg-gradient-to-br from-secondary-100 to-secondary-200 dark:from-secondary-700 dark:to-secondary-600 rounded-2xl overflow-hidden shadow-medium group">
                        @if($product->primaryImage)
                            <img :src="activeImage === 0 ? '{{ asset('storage/' . $product->primaryImage->image_path) }}' : document.querySelectorAll('.thumbnail-image')[activeImage - 1]?.src"
                                 alt="{{ $product->name }}"
                                 class="w-full h-full object-cover transition-transform duration-500 group-hover:scale-105">
                        @else
                            <div class="w-full h-full flex items-center justify-center">
                                <span class="text-8xl opacity-50">📦</span>
                            </div>
                        @endif

                        <!-- Image Navigation -->
                        @if($product->images->count() > 1)
                        <div class="absolute inset-y-0 left-0 flex items-center">
                            <button @click="activeImage = activeImage > 0 ? activeImage - 1 : {{ $product->images->count() - 1 }}"
                                    class="ml-4 p-2 bg-white/80 dark:bg-secondary-800/80 backdrop-blur-sm rounded-full shadow-medium hover:bg-white dark:hover:bg-secondary-700 transition-colors duration-200">
                                <svg class="w-5 h-5 text-secondary-600 dark:text-secondary-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
                                </svg>
                            </button>
                        </div>
                        <div class="absolute inset-y-0 right-0 flex items-center">
                            <button @click="activeImage = activeImage < {{ $product->images->count() - 1 }} ? activeImage + 1 : 0"
                                    class="mr-4 p-2 bg-white/80 dark:bg-secondary-800/80 backdrop-blur-sm rounded-full shadow-medium hover:bg-white dark:hover:bg-secondary-700 transition-colors duration-200">
                                <svg class="w-5 h-5 text-secondary-600 dark:text-secondary-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                                </svg>
                            </button>
                        </div>
                        @endif
                    </div>

                    @if($product->images->count() > 1)
                    <div class="grid grid-cols-4 gap-3">
                        <button @click="activeImage = 0"
                                :class="{ 'ring-2 ring-primary-500': activeImage === 0 }"
                                class="aspect-square bg-secondary-200 dark:bg-secondary-700 rounded-lg overflow-hidden transition-all duration-200 hover:ring-2 hover:ring-primary-300">
                            <img src="{{ asset('storage/' . $product->primaryImage->image_path) }}" alt="{{ $product->name }}" class="w-full h-full object-cover">
                        </button>
                        @foreach($product->images->skip(1)->take(3) as $index => $image)
                        <button @click="activeImage = {{ $index + 1 }}"
                                :class="{ 'ring-2 ring-primary-500': activeImage === {{ $index + 1 }} }"
                                class="aspect-square bg-secondary-200 dark:bg-secondary-700 rounded-lg overflow-hidden transition-all duration-200 hover:ring-2 hover:ring-primary-300">
                            <img src="{{ asset('storage/' . $image->image_path) }}" alt="{{ $product->name }}" class="thumbnail-image w-full h-full object-cover">
                        </button>
                        @endforeach
                    </div>
                    @endif
                </div>

                <!-- Product Details -->
                <div class="space-y-8">
                    <div class="space-y-6">
                        <!-- Product Badges -->
                        <div class="flex items-center gap-3">
                            <span class="badge {{ $product->type === 'digital' ? 'badge-success' : 'badge-primary' }}">
                                {{ ucfirst($product->type) }} Product
                            </span>
                            @if($product->category)
                            <span class="badge badge-secondary">
                                {{ $product->category->name }}
                            </span>
                            @endif
                            @if($product->compare_price && $product->compare_price > $product->price)
                            <span class="badge badge-error">
                                Save {{ $product->getFormattedSavings() }}
                            </span>
                            @endif
                        </div>

                        <!-- Product Title -->
                        <div>
                            <h1 class="text-4xl lg:text-5xl font-bold text-secondary-900 dark:text-white mb-4 leading-tight">
                                {{ $product->name }}
                            </h1>
                            @if($product->short_description)
                            <p class="text-xl text-secondary-600 dark:text-secondary-300 leading-relaxed">
                                {{ $product->short_description }}
                            </p>
                            @endif
                        </div>

                        <!-- Pricing -->
                        <div class="bg-gradient-to-r from-primary-50 to-accent-50 dark:from-primary-900/20 dark:to-accent-900/20 rounded-2xl p-6 border border-primary-200 dark:border-primary-800">
                            <div class="flex items-center gap-4 mb-4">
                                <span class="text-4xl lg:text-5xl font-bold text-secondary-900 dark:text-white">
                                    {{ $product->getFormattedPrice() }}
                                </span>
                                @if($product->compare_price && $product->compare_price > $product->price)
                                <div class="flex flex-col">
                                    <span class="text-xl text-secondary-500 dark:text-secondary-400 line-through">
                                        {{ $product->getFormattedComparePrice() }}
                                    </span>
                                    <span class="text-sm font-medium text-success-600 dark:text-success-400">
                                        You save {{ $product->getFormattedSavings() }}
                                    </span>
                                </div>
                                @endif
                            </div>

                            <!-- Stock Status -->
                            <div class="flex items-center gap-4">
                                @if($product->inStock())
                                    <div class="flex items-center">
                                        <span class="badge badge-success">
                                            <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                                            </svg>
                                            In Stock
                                        </span>
                                        @if($product->track_stock)
                                        <span class="ml-3 text-sm text-secondary-600 dark:text-secondary-400">
                                            {{ $product->stock_quantity }} available
                                            @if($product->stock_quantity <= 5)
                                            <span class="text-warning-600 dark:text-warning-400 font-medium">- Limited stock!</span>
                                            @endif
                                        </span>
                                        @endif
                                    </div>
                                @else
                                    <span class="badge badge-error">
                                        <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
                                        </svg>
                                        Out of Stock
                                    </span>
                                @endif
                            </div>
                        </div>
                    </div>

                    <!-- Product Description -->
                    <div class="bg-secondary-50 dark:bg-secondary-800/50 rounded-2xl p-6">
                        <h3 class="text-2xl font-semibold text-secondary-900 dark:text-white mb-4 flex items-center">
                            <svg class="w-6 h-6 mr-2 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                            </svg>
                            Description
                        </h3>
                        <div class="prose prose-secondary dark:prose-invert max-w-none">
                            <p class="text-secondary-700 dark:text-secondary-300 leading-relaxed text-lg">
                                {{ $product->description }}
                            </p>
                        </div>
                    </div>

                    <!-- Digital Product Info -->
                    @if($product->isDigital() && $product->digitalProduct)
                    <div class="bg-gradient-to-r from-success-50 to-primary-50 dark:from-success-900/20 dark:to-primary-900/20 border border-success-200 dark:border-success-800 rounded-2xl p-6">
                        <h3 class="text-xl font-semibold text-success-900 dark:text-success-100 mb-4 flex items-center">
                            <svg class="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"/>
                            </svg>
                            Digital Product Details
                        </h3>
                        <div class="space-y-3">
                            <div class="flex items-center">
                                <svg class="w-5 h-5 text-success-600 dark:text-success-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                                </svg>
                                <span class="text-success-800 dark:text-success-200 font-medium">Instant Download Available</span>
                            </div>
                            <div class="flex items-center">
                                <svg class="w-5 h-5 text-success-600 dark:text-success-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                                </svg>
                                <span class="text-success-800 dark:text-success-200">Available immediately after payment</span>
                            </div>
                            @if($product->digitalProduct->download_limit)
                            <div class="flex items-center">
                                <svg class="w-5 h-5 text-success-600 dark:text-success-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd"/>
                                </svg>
                                <span class="text-success-800 dark:text-success-200">{{ $product->digitalProduct->download_limit }} downloads allowed</span>
                            </div>
                            @endif
                            @if($product->digitalProduct->download_expiry_days)
                            <div class="flex items-center">
                                <svg class="w-5 h-5 text-success-600 dark:text-success-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"/>
                                </svg>
                                <span class="text-success-800 dark:text-success-200">Access expires in {{ $product->digitalProduct->download_expiry_days }} days</span>
                            </div>
                            @endif
                        </div>
                    </div>
                    @endif

                    <!-- Physical Product Info -->
                    @if($product->isPhysical())
                    <div class="bg-gradient-to-r from-secondary-50 to-primary-50 dark:from-secondary-900/20 dark:to-primary-900/20 border border-secondary-200 dark:border-secondary-800 rounded-2xl p-6">
                        <h3 class="text-xl font-semibold text-secondary-900 dark:text-secondary-100 mb-4 flex items-center">
                            <svg class="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"/>
                            </svg>
                            Shipping Information
                        </h3>
                        <div class="space-y-3">
                            <div class="flex items-center">
                                <svg class="w-5 h-5 text-secondary-600 dark:text-secondary-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                                </svg>
                                <span class="text-secondary-800 dark:text-secondary-200">Standard shipping: $10.00</span>
                            </div>
                            <div class="flex items-center">
                                <svg class="w-5 h-5 text-secondary-600 dark:text-secondary-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"/>
                                </svg>
                                <span class="text-secondary-800 dark:text-secondary-200">Estimated delivery: 3-7 business days</span>
                            </div>
                            @if($product->weight)
                            <div class="flex items-center">
                                <svg class="w-5 h-5 text-secondary-600 dark:text-secondary-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M6 6V5a3 3 0 013-3h2a3 3 0 013 3v1h2a2 2 0 012 2v3.57A22.952 22.952 0 0110 13a22.95 22.95 0 01-8-1.43V8a2 2 0 012-2h2zm2-1a1 1 0 011-1h2a1 1 0 011 1v1H8V5zm1 5a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1z" clip-rule="evenodd"/>
                                </svg>
                                <span class="text-secondary-800 dark:text-secondary-200">Weight: {{ $product->weight }} kg</span>
                            </div>
                            @endif
                        </div>
                    </div>
                    @endif

                    <!-- Crypto Payment Info -->
                    <div class="bg-gradient-to-r from-primary-50 to-accent-50 dark:from-primary-900/20 dark:to-accent-900/20 border border-primary-200 dark:border-primary-800 rounded-2xl p-6">
                        <h3 class="text-xl font-semibold text-primary-900 dark:text-primary-100 mb-4 flex items-center">
                            <svg class="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"/>
                            </svg>
                            Secure Crypto Payments
                        </h3>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div class="flex items-center">
                                <svg class="w-5 h-5 text-primary-600 dark:text-primary-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                                </svg>
                                <span class="text-primary-800 dark:text-primary-200 text-sm">Secure & Fast</span>
                            </div>
                            <div class="flex items-center">
                                <svg class="w-5 h-5 text-primary-600 dark:text-primary-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                                </svg>
                                <span class="text-primary-800 dark:text-primary-200 text-sm">Polygon Network</span>
                            </div>
                            <div class="flex items-center">
                                <svg class="w-5 h-5 text-primary-600 dark:text-primary-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                                </svg>
                                <span class="text-primary-800 dark:text-primary-200 text-sm">Decentralized</span>
                            </div>
                        </div>
                    </div>

                    <!-- Purchase Form -->
                    <div class="bg-white dark:bg-secondary-800 rounded-2xl p-6 border-2 border-primary-200 dark:border-primary-800 shadow-large">
                        <form action="{{ route('checkout.index') }}" method="GET" class="space-y-6">
                            <input type="hidden" name="product_id" value="{{ $product->id }}">

                            @if($product->inStock() && $product->track_stock && $product->stock_quantity > 1)
                            <div>
                                <label for="quantity" class="block text-sm font-medium text-secondary-700 dark:text-secondary-300 mb-3">
                                    Quantity
                                </label>
                                <select name="quantity" id="quantity" class="input w-32">
                                    @for($i = 1; $i <= min(10, $product->stock_quantity); $i++)
                                    <option value="{{ $i }}">{{ $i }}</option>
                                    @endfor
                                </select>
                            </div>
                            @endif

                            <div class="space-y-4">
                                @if($product->inStock())
                                <button type="submit" class="btn-primary btn-xl w-full group">
                                    <svg class="w-6 h-6 mr-3 group-hover:animate-bounce" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"/>
                                    </svg>
                                    Buy Now with Crypto
                                    <svg class="w-5 h-5 ml-3 group-hover:translate-x-1 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"/>
                                    </svg>
                                </button>
                                @else
                                <button disabled class="btn-secondary btn-xl w-full opacity-50 cursor-not-allowed">
                                    <svg class="w-6 h-6 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                                    </svg>
                                    Out of Stock
                                </button>
                                @endif

                                <button type="button" class="btn-secondary w-full group">
                                    <svg class="w-5 h-5 mr-2 group-hover:text-error-500 transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"/>
                                    </svg>
                                    Add to Wishlist
                                </button>
                            </div>
                        </form>
                    </div>

                    <!-- Payment Methods -->
                    <div class="border-t border-secondary-200 dark:border-secondary-600 pt-6">
                        <h4 class="text-lg font-semibold text-secondary-900 dark:text-white mb-4 flex items-center">
                            <svg class="w-5 h-5 mr-2 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"/>
                            </svg>
                            Accepted Payments
                        </h4>
                        <div class="flex flex-wrap gap-3">
                            <span class="badge badge-primary">
                                <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                                </svg>
                                USDT
                            </span>
                            <span class="badge badge-accent">
                                <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                                </svg>
                                POL
                            </span>
                            <span class="text-sm text-secondary-500 dark:text-secondary-400 flex items-center">
                                <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                                </svg>
                                on Polygon Network
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Related Products -->
        @if($relatedProducts->count() > 0)
        <div class="mt-16 animate-fade-in-up" style="animation-delay: 0.3s;">
            <div class="text-center mb-12">
                <h2 class="text-3xl lg:text-4xl font-bold text-secondary-900 dark:text-white mb-4">
                    You Might Also Like
                </h2>
                <p class="text-lg text-secondary-600 dark:text-secondary-300">
                    Discover more amazing products in our collection
                </p>
            </div>
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
                @foreach($relatedProducts as $index => $relatedProduct)
                <div class="product-card group animate-fade-in-up" style="animation-delay: {{ 0.1 * ($index + 1) }}s;">
                    <div class="product-card-image">
                        <a href="{{ route('shop.product', $relatedProduct->slug) }}">
                            @if($relatedProduct->primaryImage)
                            <img src="{{ asset('storage/' . $relatedProduct->primaryImage->image_path) }}"
                                 alt="{{ $relatedProduct->name }}"
                                 class="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110">
                            @else
                            <div class="w-full h-full bg-gradient-to-br from-secondary-200 to-secondary-300 dark:from-secondary-700 dark:to-secondary-600 flex items-center justify-center">
                                <span class="text-6xl opacity-50">📦</span>
                            </div>
                            @endif
                        </a>
                        <div class="product-card-overlay">
                            <a href="{{ route('shop.product', $relatedProduct->slug) }}" class="btn-primary btn-sm">
                                View Details
                            </a>
                        </div>
                    </div>
                    <div class="product-card-content">
                        @if($relatedProduct->category)
                        <span class="badge badge-secondary mb-2">{{ $relatedProduct->category->name }}</span>
                        @endif
                        <h3 class="product-card-title">
                            <a href="{{ route('shop.product', $relatedProduct->slug) }}" class="hover:text-primary-600 dark:hover:text-primary-400 transition-colors duration-200">
                                {{ $relatedProduct->name }}
                            </a>
                        </h3>
                        <div class="flex items-center justify-between mt-4">
                            <span class="product-card-price">
                                {{ $relatedProduct->getFormattedPrice() }}
                            </span>
                            @if($relatedProduct->inStock())
                            <span class="badge badge-success text-xs">In Stock</span>
                            @else
                            <span class="badge badge-error text-xs">Out of Stock</span>
                            @endif
                        </div>
                    </div>
                </div>
                @endforeach
            </div>
        </div>
        @endif
    </div>
</div>
@endsection
