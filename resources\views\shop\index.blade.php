@extends('layouts.shop')

@section('title', 'PolyPay Store - Cryptocurrency E-commerce')

@php
    use App\Helpers\CurrencyHelper;
@endphp

@section('content')
<div class="min-h-screen bg-secondary-50 dark:bg-secondary-900">
    <!-- Hero Section -->
    <div class="relative overflow-hidden gradient-hero">
        <!-- Background Pattern -->
        <div class="absolute inset-0 opacity-10">
            <svg class="w-full h-full" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                <defs>
                    <pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse">
                        <path d="M 10 0 L 0 0 0 10" fill="none" stroke="white" stroke-width="0.5"/>
                    </pattern>
                </defs>
                <rect width="100" height="100" fill="url(#grid)" />
            </svg>
        </div>

        <!-- Floating Elements -->
        <div class="absolute top-20 left-10 w-20 h-20 bg-white/10 rounded-full animate-float"></div>
        <div class="absolute top-40 right-20 w-16 h-16 bg-white/10 rounded-full animate-float" style="animation-delay: 1s;"></div>
        <div class="absolute bottom-20 left-1/4 w-12 h-12 bg-white/10 rounded-full animate-float" style="animation-delay: 2s;"></div>

        <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20 lg:py-32">
            <div class="text-center animate-fade-in-up">
                <h1 class="text-5xl lg:text-7xl font-bold text-white mb-6 text-balance">
                    Welcome to
                    <span class="text-gradient-accent bg-gradient-to-r from-accent-300 to-white bg-clip-text text-transparent">
                        PolyPay Store
                    </span>
                </h1>
                <p class="text-xl lg:text-2xl text-white/90 mb-10 max-w-3xl mx-auto text-balance">
                    Experience the future of e-commerce with seamless cryptocurrency payments on the Polygon network
                </p>

                <!-- Feature Badges -->
                <div class="flex flex-wrap justify-center gap-4 mb-12">
                    <div class="badge-primary backdrop-blur-glass border border-white/20 text-white">
                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4zM18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z"/>
                        </svg>
                        USDT Payments
                    </div>
                    <div class="badge-success backdrop-blur-glass border border-white/20 text-white">
                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                        </svg>
                        Instant Delivery
                    </div>
                    <div class="badge-warning backdrop-blur-glass border border-white/20 text-white">
                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd"/>
                        </svg>
                        Secure Blockchain
                    </div>
                </div>

                <!-- CTA Buttons -->
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <a href="#featured-products" class="btn-xl bg-white text-primary-600 hover:bg-secondary-50 shadow-large hover:shadow-glow">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"/>
                        </svg>
                        Start Shopping
                    </a>
                    <a href="#categories" class="btn-xl btn-outline border-white text-white hover:bg-white hover:text-primary-600">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
                        </svg>
                        Browse Categories
                    </a>
                </div>
            </div>
        </div>

        <!-- Wave Separator -->
        <div class="absolute bottom-0 left-0 right-0">
            <svg viewBox="0 0 1440 120" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M0 120L60 110C120 100 240 80 360 70C480 60 600 60 720 65C840 70 960 80 1080 85C1200 90 1320 90 1380 90L1440 90V120H1380C1320 120 1200 120 1080 120C960 120 840 120 720 120C600 120 480 120 360 120C240 120 120 120 60 120H0Z" fill="rgb(248 250 252)" class="dark:fill-secondary-900"/>
            </svg>
        </div>
    </div>

    <!-- Categories Section -->
    @if($categories->count() > 0)
    <section id="categories" class="py-20 bg-secondary-50 dark:bg-secondary-900">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16 animate-fade-in-up">
                <h2 class="text-4xl lg:text-5xl font-bold text-secondary-900 dark:text-white mb-4">
                    Shop by Category
                </h2>
                <p class="text-xl text-secondary-600 dark:text-secondary-300 max-w-2xl mx-auto">
                    Discover our carefully curated collection across various categories
                </p>
            </div>

            <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6">
                @foreach($categories as $category)
                <a href="{{ route('shop.category', $category->slug) }}" class="group animate-fade-in-up" style="animation-delay: {{ $loop->index * 0.1 }}s;">
                    <div class="card-interactive p-6 text-center h-full">
                        @if($category->image)
                        <div class="relative mb-4">
                            <img src="{{ asset('storage/' . $category->image) }}" alt="{{ $category->name }}" class="w-16 h-16 mx-auto rounded-2xl object-cover transition-transform duration-300 group-hover:scale-110">
                            <div class="absolute inset-0 bg-primary-600/20 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                        </div>
                        @else
                        <div class="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-primary-500 to-accent-500 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                            <span class="text-2xl">📦</span>
                        </div>
                        @endif
                        <h3 class="text-sm font-semibold text-secondary-900 dark:text-white group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors duration-300 mb-2">
                            {{ $category->name }}
                        </h3>
                        <p class="text-xs text-secondary-500 dark:text-secondary-400">
                            {{ $category->activeProducts->count() }} products
                        </p>
                    </div>
                </a>
                @endforeach
            </div>
        </div>
    </section>
    @endif

    <!-- Featured Products Section -->
    @if($featuredProducts->count() > 0)
    <section id="featured-products" class="py-20 bg-white dark:bg-secondary-800">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16 animate-fade-in-up">
                <h2 class="text-4xl lg:text-5xl font-bold text-secondary-900 dark:text-white mb-4">
                    Featured Products
                </h2>
                <p class="text-xl text-secondary-600 dark:text-secondary-300 max-w-2xl mx-auto">
                    Handpicked products that showcase the best of our collection
                </p>
            </div>

            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
                @foreach($featuredProducts as $product)
                <div class="product-card animate-fade-in-up" style="animation-delay: {{ $loop->index * 0.1 }}s;">
                    <div class="relative">
                        <a href="{{ route('shop.product', $product->slug) }}">
                            @if($product->primaryImage)
                            <img src="{{ asset('storage/' . $product->primaryImage->image_path) }}" alt="{{ $product->name }}" class="product-card-image">
                            @else
                            <div class="w-full h-48 bg-gradient-to-br from-secondary-200 to-secondary-300 dark:from-secondary-700 dark:to-secondary-600 flex items-center justify-center transition-all duration-500 group-hover:from-primary-200 group-hover:to-primary-300 dark:group-hover:from-primary-800 dark:group-hover:to-primary-700">
                                <span class="text-4xl">📦</span>
                            </div>
                            @endif
                        </a>

                        <!-- Product Badges -->
                        <div class="absolute top-3 left-3">
                            <span class="badge {{ $product->type === 'digital' ? 'badge-success' : 'badge-primary' }}">
                                {{ ucfirst($product->type) }}
                            </span>
                        </div>

                        @if($product->compare_price && $product->compare_price > $product->price)
                        <div class="absolute top-3 right-3">
                            <span class="badge badge-error">
                                Save {{ $product->getFormattedSavings() }}
                            </span>
                        </div>
                        @endif

                        <!-- Overlay with Quick Actions -->
                        <div class="product-card-overlay">
                            <div class="product-card-actions">
                                <div class="flex gap-2">
                                    <a href="{{ route('shop.product', $product->slug) }}" class="btn-primary flex-1 text-center">
                                        View Details
                                    </a>
                                    @if($product->inStock())
                                    <a href="{{ route('checkout.index', ['product_id' => $product->id]) }}" class="btn-success">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"/>
                                        </svg>
                                    </a>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="p-6">
                        @if($product->category)
                        <p class="text-xs font-medium text-primary-600 dark:text-primary-400 mb-2 uppercase tracking-wide">
                            {{ $product->category->name }}
                        </p>
                        @endif

                        <h3 class="text-lg font-semibold text-secondary-900 dark:text-white mb-2 line-clamp-2">
                            <a href="{{ route('shop.product', $product->slug) }}" class="hover:text-primary-600 dark:hover:text-primary-400 transition-colors duration-200">
                                {{ $product->name }}
                            </a>
                        </h3>

                        @if($product->short_description)
                        <p class="text-sm text-secondary-600 dark:text-secondary-400 mb-4 line-clamp-2">
                            {{ $product->short_description }}
                        </p>
                        @endif

                        <div class="flex items-center justify-between mb-4">
                            <div class="flex flex-col">
                                <span class="text-xl font-bold text-secondary-900 dark:text-white">
                                    {{ $product->getFormattedPrice() }}
                                </span>
                                @if($product->compare_price && $product->compare_price > $product->price)
                                <span class="text-sm text-secondary-500 dark:text-secondary-400 line-through">
                                    {{ $product->getFormattedComparePrice() }}
                                </span>
                                @endif
                            </div>

                            <div class="text-right">
                                @if($product->inStock())
                                <span class="badge badge-success">In Stock</span>
                                @if($product->track_stock && $product->stock_quantity <= 5)
                                <p class="text-xs text-warning-600 mt-1">Only {{ $product->stock_quantity }} left</p>
                                @endif
                                @else
                                <span class="badge badge-error">Out of Stock</span>
                                @endif
                            </div>
                        </div>

                        <div class="flex gap-2">
                            <a href="{{ route('shop.product', $product->slug) }}" class="btn-secondary flex-1 text-center">
                                View Details
                            </a>
                            @if($product->inStock())
                            <a href="{{ route('checkout.index', ['product_id' => $product->id]) }}" class="btn-primary">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"/>
                                </svg>
                            </a>
                            @endif
                        </div>
                    </div>
                </div>
                @endforeach
            </div>
        </div>
    </section>
    @endif

    <!-- Call to Action -->
    <section class="relative py-20 gradient-hero overflow-hidden">
        <!-- Background Elements -->
        <div class="absolute inset-0 opacity-10">
            <div class="absolute top-10 left-10 w-32 h-32 bg-white/20 rounded-full animate-pulse-soft"></div>
            <div class="absolute bottom-10 right-10 w-24 h-24 bg-white/20 rounded-full animate-bounce-gentle"></div>
            <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-40 h-40 bg-white/10 rounded-full animate-float"></div>
        </div>

        <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <div class="animate-fade-in-up">
                <h2 class="text-4xl lg:text-5xl font-bold text-white mb-6">
                    Ready to Shop with
                    <span class="text-gradient-accent bg-gradient-to-r from-accent-300 to-white bg-clip-text text-transparent">
                        Crypto?
                    </span>
                </h2>
                <p class="text-xl lg:text-2xl text-white/90 mb-10 max-w-3xl mx-auto">
                    Experience secure, fast, and decentralized payments on the Polygon network
                </p>

                <!-- Stats -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
                    <div class="text-center">
                        <div class="text-3xl lg:text-4xl font-bold text-white mb-2">{{ $featuredProducts->count() }}+</div>
                        <div class="text-white/80">Featured Products</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl lg:text-4xl font-bold text-white mb-2">{{ $categories->count() }}+</div>
                        <div class="text-white/80">Categories</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl lg:text-4xl font-bold text-white mb-2">100%</div>
                        <div class="text-white/80">Secure Payments</div>
                    </div>
                </div>

                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <a href="{{ route('shop.index') }}" class="btn-xl bg-white text-primary-600 hover:bg-secondary-50 shadow-large hover:shadow-glow">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
                        </svg>
                        Browse All Products
                    </a>
                    @auth
                    <a href="{{ route('dashboard') }}" class="btn-xl btn-outline border-white text-white hover:bg-white hover:text-primary-600">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                        </svg>
                        My Dashboard
                    </a>
                    @else
                    <a href="{{ route('register') }}" class="btn-xl btn-outline border-white text-white hover:bg-white hover:text-primary-600">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"/>
                        </svg>
                        Join Now
                    </a>
                    @endauth
                </div>
            </div>
        </div>
    </section>
</div>

<!-- Add JavaScript for scroll animations -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Animate elements on scroll
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('in-view');
            }
        });
    }, observerOptions);

    document.querySelectorAll('.animate-on-scroll').forEach(el => {
        observer.observe(el);
    });
});
</script>
@endsection
