@extends('layouts.shop')

@section('title', $category->name . ' - PolyPay Store')

@section('content')
<div class="min-h-screen bg-gray-50 dark:bg-gray-900 py-12">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Category Header -->
        <div class="bg-white dark:bg-gray-800 shadow-sm rounded-lg p-8 mb-8">
            <div class="text-center">
                @if($category->image)
                <img src="{{ asset('storage/' . $category->image) }}" alt="{{ $category->name }}" class="w-24 h-24 mx-auto mb-4 rounded-full object-cover">
                @else
                <div class="w-24 h-24 mx-auto mb-4 bg-gray-200 dark:bg-gray-700 rounded-full flex items-center justify-center">
                    <span class="text-4xl">📦</span>
                </div>
                @endif
                <h1 class="text-4xl font-bold text-gray-900 dark:text-white mb-4">{{ $category->name }}</h1>
                @if($category->description)
                <p class="text-xl text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">{{ $category->description }}</p>
                @endif
                <div class="mt-4">
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                        {{ $products->total() }} {{ Str::plural('product', $products->total()) }}
                    </span>
                </div>
            </div>
        </div>

        <!-- Filters and Sorting -->
        <div class="bg-white dark:bg-gray-800 shadow-sm rounded-lg p-6 mb-8">
            <form method="GET" action="{{ route('shop.category', $category->slug) }}" class="flex flex-wrap items-center gap-4">
                <!-- Sort -->
                <div class="flex items-center space-x-2">
                    <label for="sort" class="text-sm font-medium text-gray-700 dark:text-gray-300">Sort by:</label>
                    <select name="sort" id="sort" onchange="this.form.submit()" class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white">
                        <option value="newest" {{ request('sort') === 'newest' ? 'selected' : '' }}>Newest</option>
                        <option value="price_low" {{ request('sort') === 'price_low' ? 'selected' : '' }}>Price: Low to High</option>
                        <option value="price_high" {{ request('sort') === 'price_high' ? 'selected' : '' }}>Price: High to Low</option>
                        <option value="name" {{ request('sort') === 'name' ? 'selected' : '' }}>Name A-Z</option>
                    </select>
                </div>

                <!-- Product Type Filter -->
                <div class="flex items-center space-x-2">
                    <label for="type" class="text-sm font-medium text-gray-700 dark:text-gray-300">Type:</label>
                    <select name="type" id="type" onchange="this.form.submit()" class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white">
                        <option value="">All Types</option>
                        <option value="physical" {{ request('type') === 'physical' ? 'selected' : '' }}>Physical</option>
                        <option value="digital" {{ request('type') === 'digital' ? 'selected' : '' }}>Digital</option>
                    </select>
                </div>

                <!-- Price Range -->
                <div class="flex items-center space-x-2">
                    <label class="text-sm font-medium text-gray-700 dark:text-gray-300">Price:</label>
                    <input type="number" name="min_price" placeholder="Min" value="{{ request('min_price') }}" class="w-20 px-2 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white">
                    <span class="text-gray-500">-</span>
                    <input type="number" name="max_price" placeholder="Max" value="{{ request('max_price') }}" class="w-20 px-2 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white">
                    <button type="submit" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors duration-300">
                        Filter
                    </button>
                </div>

                @if(request()->hasAny(['sort', 'type', 'min_price', 'max_price']))
                <a href="{{ route('shop.category', $category->slug) }}" class="px-4 py-2 bg-gray-300 hover:bg-gray-400 text-gray-700 rounded-md transition-colors duration-300">
                    Clear Filters
                </a>
                @endif
            </form>
        </div>

        <!-- Products Grid -->
        @if($products->count() > 0)
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8 mb-8">
            @foreach($products as $product)
            <div class="product-card animate-fade-in-up" style="animation-delay: {{ $loop->index * 0.05 }}s;">
                <div class="relative">
                    <a href="{{ route('shop.product', $product->slug) }}">
                        @if($product->primaryImage)
                        <img src="{{ asset('storage/' . $product->primaryImage->image_path) }}" alt="{{ $product->name }}" class="product-card-image">
                        @else
                        <div class="w-full h-48 bg-gradient-to-br from-secondary-200 to-secondary-300 dark:from-secondary-700 dark:to-secondary-600 flex items-center justify-center transition-all duration-500 group-hover:from-primary-200 group-hover:to-primary-300 dark:group-hover:from-primary-800 dark:group-hover:to-primary-700">
                            <span class="text-4xl">📦</span>
                        </div>
                        @endif
                    </a>

                    <!-- Product Badges -->
                    <div class="absolute top-3 left-3">
                        <span class="badge {{ $product->type === 'digital' ? 'badge-success' : 'badge-primary' }}">
                            {{ ucfirst($product->type) }}
                        </span>
                    </div>

                    @if($product->compare_price && $product->compare_price > $product->price)
                    <div class="absolute top-3 right-3">
                        <span class="badge badge-error">
                            Save {{ $product->getFormattedSavings() }}
                        </span>
                    </div>
                    @endif

                    <!-- Overlay with Quick Actions -->
                    <div class="product-card-overlay">
                        <div class="product-card-actions">
                            <div class="flex gap-2">
                                <a href="{{ route('shop.product', $product->slug) }}" class="btn-primary flex-1 text-center">
                                    View Details
                                </a>
                                @if($product->inStock())
                                <a href="{{ route('checkout.index', ['product_id' => $product->id]) }}" class="btn-success">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"/>
                                    </svg>
                                </a>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>

                <div class="p-6">
                    <h3 class="text-lg font-semibold text-secondary-900 dark:text-white mb-2 line-clamp-2">
                        <a href="{{ route('shop.product', $product->slug) }}" class="hover:text-primary-600 dark:hover:text-primary-400 transition-colors duration-200">
                            {{ $product->name }}
                        </a>
                    </h3>

                    @if($product->short_description)
                    <p class="text-sm text-secondary-600 dark:text-secondary-400 mb-4 line-clamp-2">
                        {{ $product->short_description }}
                    </p>
                    @endif

                    <div class="flex items-center justify-between mb-4">
                        <div class="flex flex-col">
                            <span class="text-xl font-bold text-secondary-900 dark:text-white">
                                {{ $product->getFormattedPrice() }}
                            </span>
                            @if($product->compare_price && $product->compare_price > $product->price)
                            <span class="text-sm text-secondary-500 dark:text-secondary-400 line-through">
                                {{ $product->getFormattedComparePrice() }}
                            </span>
                            @endif
                        </div>

                        <div class="text-right">
                            @if($product->inStock())
                            <span class="badge badge-success">In Stock</span>
                            @if($product->track_stock && $product->stock_quantity <= 5)
                            <p class="text-xs text-warning-600 mt-1">Only {{ $product->stock_quantity }} left</p>
                            @endif
                            @else
                            <span class="badge badge-error">Out of Stock</span>
                            @endif
                        </div>
                    </div>

                    <div class="flex gap-2">
                        <a href="{{ route('shop.product', $product->slug) }}" class="btn-secondary flex-1 text-center">
                            View Details
                        </a>
                        @if($product->inStock())
                        <a href="{{ route('checkout.index', ['product_id' => $product->id]) }}" class="btn-primary">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"/>
                            </svg>
                        </a>
                        @else
                        <button disabled class="btn-secondary opacity-50 cursor-not-allowed">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                            </svg>
                        </button>
                        @endif
                    </div>
                </div>
            </div>
            @endforeach
        </div>

        <!-- Pagination -->
        @if($products->hasPages())
        <div class="flex justify-center">
            {{ $products->appends(request()->query())->links() }}
        </div>
        @endif

        @else
        <!-- Empty State -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-12 text-center">
            <div class="w-24 h-24 mx-auto mb-4 bg-gray-200 dark:bg-gray-700 rounded-full flex items-center justify-center">
                <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2M4 13h2m13-8V4a1 1 0 00-1-1H7a1 1 0 00-1 1v1m8 0V4.5"/>
                </svg>
            </div>
            <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">No products found</h3>
            <p class="text-gray-600 dark:text-gray-400 mb-6">
                We couldn't find any products in this category matching your criteria.
            </p>
            <div class="space-x-4">
                <a href="{{ route('shop.category', $category->slug) }}" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-6 rounded-lg transition-colors duration-300">
                    Clear Filters
                </a>
                <a href="{{ route('shop.index') }}" class="bg-gray-300 hover:bg-gray-400 text-gray-700 font-medium py-2 px-6 rounded-lg transition-colors duration-300">
                    Browse All Products
                </a>
            </div>
        </div>
        @endif

        <!-- Category Navigation -->
        <div class="mt-12 bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Browse Other Categories</h3>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                @foreach(\App\Models\Category::active()->where('id', '!=', $category->id)->take(4)->get() as $otherCategory)
                <a href="{{ route('shop.category', $otherCategory->slug) }}" class="group">
                    <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 text-center hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors duration-300">
                        @if($otherCategory->image)
                        <img src="{{ asset('storage/' . $otherCategory->image) }}" alt="{{ $otherCategory->name }}" class="w-12 h-12 mx-auto mb-2 rounded-full object-cover">
                        @else
                        <div class="w-12 h-12 mx-auto mb-2 bg-gray-200 dark:bg-gray-600 rounded-full flex items-center justify-center">
                            <span class="text-xl">📦</span>
                        </div>
                        @endif
                        <h4 class="text-sm font-medium text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400">
                            {{ $otherCategory->name }}
                        </h4>
                        <p class="text-xs text-gray-500 dark:text-gray-400">
                            {{ $otherCategory->activeProducts->count() }} products
                        </p>
                    </div>
                </a>
                @endforeach
            </div>
        </div>
    </div>
</div>
@endsection
