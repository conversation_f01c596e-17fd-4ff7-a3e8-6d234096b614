@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
    html {
        scroll-behavior: smooth;
    }

    body {
        @apply font-sans antialiased;
        font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
    }

    h1, h2, h3, h4, h5, h6 {
        @apply font-display;
        font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
    }
}

@layer components {
    /* Button Components */
    .btn {
        @apply inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
    }

    .btn-primary {
        @apply btn bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500 shadow-soft hover:shadow-medium;
    }

    .btn-secondary {
        @apply btn bg-secondary-100 text-secondary-900 hover:bg-secondary-200 focus:ring-secondary-500 dark:bg-secondary-800 dark:text-secondary-100 dark:hover:bg-secondary-700;
    }

    .btn-success {
        @apply btn bg-success-600 text-white hover:bg-success-700 focus:ring-success-500 shadow-soft hover:shadow-medium;
    }

    .btn-warning {
        @apply btn bg-warning-600 text-white hover:bg-warning-700 focus:ring-warning-500 shadow-soft hover:shadow-medium;
    }

    .btn-error {
        @apply btn bg-error-600 text-white hover:bg-error-700 focus:ring-error-500 shadow-soft hover:shadow-medium;
    }

    .btn-outline {
        @apply btn border-2 border-primary-600 text-primary-600 hover:bg-primary-600 hover:text-white focus:ring-primary-500;
    }

    .btn-ghost {
        @apply btn text-secondary-600 hover:bg-secondary-100 focus:ring-secondary-500 dark:text-secondary-400 dark:hover:bg-secondary-800;
    }

    .btn-lg {
        @apply px-6 py-3 text-base;
    }

    .btn-xl {
        @apply px-8 py-4 text-lg;
    }

    .btn-sm {
        @apply px-3 py-1.5 text-xs;
    }

    /* Card Components */
    .card {
        @apply bg-white dark:bg-secondary-800 rounded-xl shadow-soft border border-secondary-200 dark:border-secondary-700;
    }

    .card-hover {
        @apply card transition-all duration-300 hover:shadow-medium hover:-translate-y-1;
    }

    .card-interactive {
        @apply card-hover cursor-pointer hover:border-primary-300 dark:hover:border-primary-600;
    }

    /* Input Components */
    .input {
        @apply block w-full px-3 py-2 border border-secondary-300 dark:border-secondary-600 rounded-lg shadow-sm placeholder-secondary-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-secondary-700 dark:text-white transition-colors duration-200;
    }

    .input-lg {
        @apply input px-4 py-3 text-lg;
    }

    /* Badge Components */
    .badge {
        @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
    }

    .badge-primary {
        @apply badge bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-200;
    }

    .badge-secondary {
        @apply badge bg-secondary-100 text-secondary-800 dark:bg-secondary-800 dark:text-secondary-200;
    }

    .badge-success {
        @apply badge bg-success-100 text-success-800 dark:bg-success-900 dark:text-success-200;
    }

    .badge-warning {
        @apply badge bg-warning-100 text-warning-800 dark:bg-warning-900 dark:text-warning-200;
    }

    .badge-error {
        @apply badge bg-error-100 text-error-800 dark:bg-error-900 dark:text-error-200;
    }

    /* Product Card */
    .product-card {
        @apply card-interactive overflow-hidden;
    }

    .product-card-image {
        @apply w-full h-48 object-cover transition-transform duration-500 group-hover:scale-110;
    }

    .product-card-overlay {
        @apply absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300;
    }

    .product-card-actions {
        @apply absolute bottom-4 left-4 right-4 transform translate-y-4 opacity-0 group-hover:translate-y-0 group-hover:opacity-100 transition-all duration-300;
    }

    /* Navigation */
    .nav-link {
        @apply px-3 py-2 text-sm font-medium text-secondary-700 dark:text-secondary-300 hover:text-primary-600 dark:hover:text-primary-400 transition-colors duration-200;
    }

    .nav-link-active {
        @apply nav-link text-primary-600 dark:text-primary-400;
    }

    /* Gradients */
    .gradient-primary {
        @apply bg-gradient-to-r from-primary-600 to-primary-700;
    }

    .gradient-secondary {
        @apply bg-gradient-to-r from-secondary-600 to-secondary-700;
    }

    .gradient-accent {
        @apply bg-gradient-to-r from-accent-600 to-accent-700;
    }

    .gradient-hero {
        @apply bg-gradient-to-br from-primary-600 via-primary-700 to-accent-600;
    }

    /* Text Gradients */
    .text-gradient-primary {
        @apply bg-gradient-to-r from-primary-600 to-primary-700 bg-clip-text text-transparent;
    }

    .text-gradient-accent {
        @apply bg-gradient-to-r from-accent-600 to-primary-600 bg-clip-text text-transparent;
    }

    /* Animations */
    .animate-on-scroll {
        @apply opacity-0 translate-y-8 transition-all duration-700 ease-out;
    }

    .animate-on-scroll.in-view {
        @apply opacity-100 translate-y-0;
    }

    /* Loading States */
    .loading-skeleton {
        @apply animate-pulse bg-secondary-200 dark:bg-secondary-700 rounded;
    }

    .loading-shimmer {
        @apply relative overflow-hidden bg-secondary-200 dark:bg-secondary-700;
    }

    .loading-shimmer::after {
        @apply absolute inset-0 -translate-x-full bg-gradient-to-r from-transparent via-white/20 to-transparent;
        content: '';
        animation: shimmer 2s infinite;
    }

    /* Utilities */
    .text-balance {
        text-wrap: balance;
    }

    .backdrop-blur-glass {
        backdrop-filter: blur(10px);
        background: rgba(255, 255, 255, 0.1);
    }

    .dark .backdrop-blur-glass {
        background: rgba(0, 0, 0, 0.2);
    }
}

@layer utilities {
    .scrollbar-hide {
        -ms-overflow-style: none;
        scrollbar-width: none;
    }

    .scrollbar-hide::-webkit-scrollbar {
        display: none;
    }

    .line-clamp-2 {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    .line-clamp-3 {
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }
}

@keyframes shimmer {
    100% {
        transform: translateX(100%);
    }
}

/* Advanced Animation Keyframes */
@keyframes loading-shimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

@keyframes loading-spin {
    to {
        transform: rotate(360deg);
    }
}

@keyframes loading-dots {
    0%, 20% {
        content: '';
    }
    40% {
        content: '.';
    }
    60% {
        content: '..';
    }
    80%, 100% {
        content: '...';
    }
}

@keyframes typewriter {
    from {
        width: 0;
    }
    to {
        width: 100%;
    }
}

@keyframes blink {
    50% {
        border-color: transparent;
    }
}

/* Advanced Utility Classes */
@layer utilities {
    .text-gradient-primary {
        @apply bg-gradient-to-r from-primary-600 to-accent-600 bg-clip-text text-transparent;
    }

    .text-gradient-accent {
        @apply bg-gradient-to-r from-accent-400 to-primary-400 bg-clip-text text-transparent;
    }

    .gradient-hero {
        @apply bg-gradient-to-br from-primary-600 via-primary-700 to-accent-700;
    }

    .gradient-card {
        @apply bg-gradient-to-br from-white to-secondary-50 dark:from-secondary-800 dark:to-secondary-900;
    }

    .scrollbar-hide {
        -ms-overflow-style: none;
        scrollbar-width: none;
    }

    .scrollbar-hide::-webkit-scrollbar {
        display: none;
    }

    .line-clamp-2 {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    .line-clamp-3 {
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    /* Loading States */
    .loading-skeleton {
        @apply bg-gradient-to-r from-secondary-200 via-secondary-300 to-secondary-200 dark:from-secondary-700 dark:via-secondary-600 dark:to-secondary-700;
        background-size: 200% 100%;
        animation: loading-shimmer 1.5s infinite;
    }

    .loading-spinner {
        animation: loading-spin 1s linear infinite;
    }

    .loading-dots::after {
        content: '';
        animation: loading-dots 1.5s infinite;
    }

    /* Hover Effects */
    .hover-lift {
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .hover-lift:hover {
        transform: translateY(-4px);
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    }

    .hover-scale {
        transition: transform 0.3s ease;
    }

    .hover-scale:hover {
        transform: scale(1.05);
    }

    .hover-glow {
        transition: box-shadow 0.3s ease;
    }

    .hover-glow:hover {
        box-shadow: 0 0 20px rgba(var(--primary-500), 0.4);
    }

    /* Interactive Elements */
    .interactive-element {
        @apply transition-all duration-300 ease-in-out;
    }

    .interactive-element:hover {
        @apply transform scale-105;
    }

    .interactive-element:active {
        @apply transform scale-95;
    }

    /* Scroll Animations */
    .animate-on-scroll {
        opacity: 0;
        transform: translateY(30px);
        transition: opacity 0.6s ease, transform 0.6s ease;
    }

    .animate-on-scroll.in-view {
        opacity: 1;
        transform: translateY(0);
    }

    /* Navigation Styles */
    .nav-link {
        @apply flex items-center px-4 py-2 text-sm font-medium text-secondary-700 dark:text-secondary-300 hover:text-primary-600 dark:hover:text-primary-400 hover:bg-secondary-100 dark:hover:bg-secondary-700 rounded-lg transition-all duration-200;
    }

    .nav-link-active {
        @apply text-primary-600 dark:text-primary-400 bg-primary-50 dark:bg-primary-900/20;
    }

    /* Glass Effect */
    .glass-effect {
        @apply bg-white/10 backdrop-blur-md border border-white/20;
    }

    .glass-effect-dark {
        @apply bg-black/10 backdrop-blur-md border border-black/20;
    }

    /* Micro-interactions */
    .micro-bounce {
        animation: micro-bounce 0.6s ease-in-out;
    }

    .micro-shake {
        animation: micro-shake 0.5s ease-in-out;
    }

    .micro-pulse {
        animation: micro-pulse 2s infinite;
    }
}

/* Micro-interaction Keyframes */
@keyframes micro-bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0, 0, 0);
    }
    40%, 43% {
        transform: translate3d(0, -8px, 0);
    }
    70% {
        transform: translate3d(0, -4px, 0);
    }
    90% {
        transform: translate3d(0, -2px, 0);
    }
}

@keyframes micro-shake {
    10%, 90% {
        transform: translate3d(-1px, 0, 0);
    }
    20%, 80% {
        transform: translate3d(2px, 0, 0);
    }
    30%, 50%, 70% {
        transform: translate3d(-4px, 0, 0);
    }
    40%, 60% {
        transform: translate3d(4px, 0, 0);
    }
}

@keyframes micro-pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}
