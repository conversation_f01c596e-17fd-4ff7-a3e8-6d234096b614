@extends('layouts.shop')

@section('title', 'Search Results for "' . $query . '" - PolyPay Store')

@section('content')
<div class="min-h-screen bg-gray-50 dark:bg-gray-900 py-12">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Search Header -->
        <div class="bg-white dark:bg-gray-800 shadow-sm rounded-lg p-6 mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Search Results</h1>
                    <p class="text-gray-600 dark:text-gray-400 mt-1">
                        Found {{ $products->total() }} {{ Str::plural('result', $products->total()) }} for "<span class="font-semibold">{{ $query }}</span>"
                    </p>
                </div>
                
                <!-- Search Form -->
                <div class="w-full max-w-md">
                    <form action="{{ route('shop.search') }}" method="GET">
                        <div class="relative">
                            <input type="text" name="q" value="{{ $query }}" placeholder="Search products..." 
                                class="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                                </svg>
                            </div>
                            <button type="submit" class="absolute inset-y-0 right-0 pr-3 flex items-center">
                                <svg class="h-5 w-5 text-gray-400 hover:text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                                </svg>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        @if($products->count() > 0)
        <!-- Products Grid -->
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8 mb-8">
            @foreach($products as $product)
            <div class="product-card animate-fade-in-up" style="animation-delay: {{ $loop->index * 0.05 }}s;">
                <div class="relative">
                    <a href="{{ route('shop.product', $product->slug) }}">
                        @if($product->primaryImage)
                        <img src="{{ asset('storage/' . $product->primaryImage->image_path) }}" alt="{{ $product->name }}" class="product-card-image">
                        @else
                        <div class="w-full h-48 bg-gradient-to-br from-secondary-200 to-secondary-300 dark:from-secondary-700 dark:to-secondary-600 flex items-center justify-center transition-all duration-500 group-hover:from-primary-200 group-hover:to-primary-300 dark:group-hover:from-primary-800 dark:group-hover:to-primary-700">
                            <span class="text-4xl">📦</span>
                        </div>
                        @endif
                    </a>

                    <!-- Product Badges -->
                    <div class="absolute top-3 left-3 flex flex-col gap-2">
                        <span class="badge {{ $product->type === 'digital' ? 'badge-success' : 'badge-primary' }}">
                            {{ ucfirst($product->type) }}
                        </span>
                        @if($product->category)
                        <span class="badge badge-secondary">
                            {{ $product->category->name }}
                        </span>
                        @endif
                    </div>

                    @if($product->compare_price && $product->compare_price > $product->price)
                    <div class="absolute top-3 right-3">
                        <span class="badge badge-error">
                            Save {{ $product->getFormattedSavings() }}
                        </span>
                    </div>
                    @endif

                    <!-- Overlay with Quick Actions -->
                    <div class="product-card-overlay">
                        <div class="product-card-actions">
                            <div class="flex gap-2">
                                <a href="{{ route('shop.product', $product->slug) }}" class="btn-primary flex-1 text-center">
                                    View Details
                                </a>
                                @if($product->inStock())
                                <a href="{{ route('checkout.index', ['product_id' => $product->id]) }}" class="btn-success">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"/>
                                    </svg>
                                </a>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="p-4">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2 line-clamp-2">
                        <a href="{{ route('shop.product', $product->slug) }}" class="hover:text-blue-600 dark:hover:text-blue-400">
                            {{ $product->name }}
                        </a>
                    </h3>
                    
                    @if($product->short_description)
                    <p class="text-sm text-gray-600 dark:text-gray-400 mb-3 line-clamp-2">
                        {{ $product->short_description }}
                    </p>
                    @endif

                    <div class="flex items-center justify-between mb-3">
                        <div class="flex flex-col">
                            <span class="text-lg font-bold text-gray-900 dark:text-white">
                                ${{ number_format($product->price, 2) }}
                            </span>
                            @if($product->compare_price && $product->compare_price > $product->price)
                            <span class="text-sm text-gray-500 dark:text-gray-400 line-through">
                                ${{ number_format($product->compare_price, 2) }}
                            </span>
                            @endif
                        </div>
                        
                        <div class="text-right">
                            @if($product->inStock())
                            <span class="text-sm text-green-600 dark:text-green-400 font-medium">In Stock</span>
                            @else
                            <span class="text-sm text-red-600 dark:text-red-400 font-medium">Out of Stock</span>
                            @endif
                        </div>
                    </div>

                    <div class="flex space-x-2">
                        <a href="{{ route('checkout.index', ['product_id' => $product->id]) }}" 
                           class="flex-1 bg-blue-600 hover:bg-blue-700 text-white text-center py-2 px-4 rounded-lg transition-colors duration-300 text-sm font-medium {{ !$product->inStock() ? 'opacity-50 cursor-not-allowed' : '' }}"
                           {{ !$product->inStock() ? 'onclick="return false;"' : '' }}>
                            {{ $product->inStock() ? 'Buy Now' : 'Out of Stock' }}
                        </a>
                        <button class="px-3 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-300">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"/>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
            @endforeach
        </div>

        <!-- Pagination -->
        @if($products->hasPages())
        <div class="flex justify-center">
            {{ $products->appends(['q' => $query])->links() }}
        </div>
        @endif

        @else
        <!-- No Results -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-12 text-center">
            <div class="w-24 h-24 mx-auto mb-4 bg-gray-200 dark:bg-gray-700 rounded-full flex items-center justify-center">
                <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                </svg>
            </div>
            <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">No products found</h3>
            <p class="text-gray-600 dark:text-gray-400 mb-6">
                We couldn't find any products matching "<span class="font-semibold">{{ $query }}</span>". Try searching with different keywords.
            </p>
            
            <!-- Search Suggestions -->
            <div class="mb-6">
                <h4 class="text-lg font-medium text-gray-900 dark:text-white mb-3">Search Suggestions:</h4>
                <div class="flex flex-wrap justify-center gap-2">
                    <a href="{{ route('shop.search', ['q' => 'laptop']) }}" class="px-3 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-full text-sm hover:bg-gray-200 dark:hover:bg-gray-600">laptop</a>
                    <a href="{{ route('shop.search', ['q' => 'digital']) }}" class="px-3 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-full text-sm hover:bg-gray-200 dark:hover:bg-gray-600">digital</a>
                    <a href="{{ route('shop.search', ['q' => 'crypto']) }}" class="px-3 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-full text-sm hover:bg-gray-200 dark:hover:bg-gray-600">crypto</a>
                    <a href="{{ route('shop.search', ['q' => 'book']) }}" class="px-3 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-full text-sm hover:bg-gray-200 dark:hover:bg-gray-600">book</a>
                </div>
            </div>
            
            <div class="space-x-4">
                <a href="{{ route('shop.index') }}" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-6 rounded-lg transition-colors duration-300">
                    Browse All Products
                </a>
            </div>
        </div>
        @endif

        <!-- Popular Categories -->
        <div class="mt-12 bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Popular Categories</h3>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                @foreach(\App\Models\Category::active()->take(4)->get() as $category)
                <a href="{{ route('shop.category', $category->slug) }}" class="group">
                    <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 text-center hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors duration-300">
                        @if($category->image)
                        <img src="{{ asset('storage/' . $category->image) }}" alt="{{ $category->name }}" class="w-12 h-12 mx-auto mb-2 rounded-full object-cover">
                        @else
                        <div class="w-12 h-12 mx-auto mb-2 bg-gray-200 dark:bg-gray-600 rounded-full flex items-center justify-center">
                            <span class="text-xl">📦</span>
                        </div>
                        @endif
                        <h4 class="text-sm font-medium text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400">
                            {{ $category->name }}
                        </h4>
                        <p class="text-xs text-gray-500 dark:text-gray-400">
                            {{ $category->activeProducts->count() }} products
                        </p>
                    </div>
                </a>
                @endforeach
            </div>
        </div>
    </div>
</div>
@endsection
